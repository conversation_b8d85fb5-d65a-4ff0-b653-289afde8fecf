import { useState } from 'react'
import { Trans } from 'react-i18next'
import Button from '../../../common/components/Button'
import Divider from '../../../common/components/Divider'
import Layout from '../../../common/components/Layout'
import TextError from '../../../common/components/TextError'
import TextInput from '../../../common/components/TextInput'
import { CONSTANTS } from '../../../common/constants/ConstantValues'
import { UI_TEST_ID } from '../../../common/constants/DataTestIDs'
import { INPUT_LIMIT } from '../../../common/constants/InputLimits'
import { inputRestrictionRegex } from '../../../common/constants/Regex'
import { useDeviceScreen } from '../../../common/hooks/useDeviceScreen'
import { faceTecCompatibleBrowser } from '../../../common/utils/UtilFunctions'
import { PUBLIC } from '../../../routes/Route'
import { isDisabled } from '../../DisabledLaunchFeatures'
import ExpiredSessionModal from '../components/ExpiredSessionModal'
import FaceTecUnsupportedBrowserMessage from '../components/FaceTecUnsupportedBrowserMessage'
import SignUpButton from '../components/SignUpButton'
import SuccessModal from '../components/SuccessModal'
import SupportedBrowsers from '../components/SupportedBrowsers'
import { useLoginForm } from '../hooks/useLoginForm'
import style from '../style/LoginForm.module.scss'
import FaceScan from './FaceScan'

/**
 * This component renders the login form. It includes email
 * input, FaceTec biometric login, and magic link login options.
 * The component handles various states such as email validation,
 * session expiration, and FaceTec compatibility checks.
 */
const LoginForm = () => {
  const {
    t,
    userEmail,
    emailSent,
    emailValidated,
    validateEmail,
    handleTyping,
    currentState,
    error,
    faceTecLogin,
    setFaceTecLogin,
    isLoginButtonDisabled,
    showFaceTecAuthentication,
    handleMagicLogin,
    sessionExpired,
    dismissExpiredSessionModal,
    resetAuthMachineToNoAuthState,
  } = useLoginForm()
  const { isMobileOrTablet } = useDeviceScreen()
  const loadingButtonState = currentState === 'SENDING_NEW_TAB_EMAIL'

  const isFaceTecCompatibleBrowser = faceTecCompatibleBrowser(isMobileOrTablet)
  const [renderunsupBrowserModal, setRenderunsupBrowserModal] = useState(false)

  return (
    <main className={style['login-form']}>
      {renderunsupBrowserModal ? (
        <SupportedBrowsers
          setRenderunsupBrowserModal={setRenderunsupBrowserModal}
        />
      ) : (
        <>
          {emailSent && (
            <SuccessModal
              userEmail={userEmail}
              handleMagicLogin={handleMagicLogin}
              resetAuthMachineToNoAuthState={resetAuthMachineToNoAuthState}
            />
          )}

          {sessionExpired?.renderNotification && (
            <ExpiredSessionModal
              sessionExpired={sessionExpired}
              dismissExpiredSessionModal={dismissExpiredSessionModal}
            />
          )}

          {faceTecLogin && (
            <FaceScan
              email={userEmail}
              asModal
              scanType={'auth-scan'}
              onClickExitScan={() => setFaceTecLogin(false)}
              showMobileBar
            />
          )}

          <Layout
            headerTitle={t('LOGIN_FORM.HEADER_TITLE')}
            navigateTo={PUBLIC.HOME}
            headerVariant="spaced"
            headerTextColor="blue"
            containerWidth="small"
            containerHeight="auto"
            containerMt="nomt"
            hideDividerHeader
            hideMobileHeader={faceTecLogin}
          >
            <section className={style[`login-form__main-content`]}>
              <Divider
                number={CONSTANTS.STEP_ONE}
                className={style[`login-form__divider-top`]}
              />

              <TextInput
                onChange={handleTyping}
                value={userEmail}
                inputMode="email"
                placeholder={t('EMAIL_PLACEHOLDER_TEXT')}
                label={t('INPUT_LABEL.SIGN_UP_EMAIL')}
                validatorFunction={validateEmail}
                errorMessage={emailValidated}
                maxLength={INPUT_LIMIT.GENERIC_MAX}
                autoComplete="on"
                dataTestID={UI_TEST_ID.emailInput}
                restrictionRegex={inputRestrictionRegex.noWhiteSpace}
                optional
              />

              <Divider
                number={CONSTANTS.STEP_TWO}
                className={style[`login-form__divider`]}
              />

              <section className={style['login-form__buttons']}>
                {error && (
                  <TextError
                    errorText={error?.translatedError}
                    className={style['login-form__error-message']}
                  />
                )}
                <Button
                  disabled={
                    (!isFaceTecCompatibleBrowser
                      ? !isFaceTecCompatibleBrowser
                      : isLoginButtonDisabled) || isDisabled
                  }
                  variant="primary"
                  onClick={showFaceTecAuthentication}
                  dataTestID={UI_TEST_ID.faceScanLoginButton}
                  loading={loadingButtonState}
                >
                  {t('LOGIN_FORM.FACETEC_LOGIN_BUTTON')}
                </Button>
                {!isFaceTecCompatibleBrowser && (
                  <FaceTecUnsupportedBrowserMessage
                    setRenderunsupBrowserModal={setRenderunsupBrowserModal}
                  />
                )}
                <p className={style['login-form__btn-separator']}>
                  {t('BUTTON_SEPARATOR')}
                </p>
                <Button
                  onClick={handleMagicLogin}
                  variant="alternative"
                  disabled={isLoginButtonDisabled}
                  loading={loadingButtonState}
                >
                  {t('LOGIN_FORM.LOGIN_BUTTON_LEGACY')}
                </Button>
                <p className={style['login-form__explainer-text']}>
                  <Trans i18nKey={'LOGIN_FORM.ACCOUNT_ACCESS_EXPLAINER'} />
                </p>
              </section>
            </section>
          </Layout>
          <SignUpButton />
        </>
      )}
    </main>
  )
}

export default LoginForm
