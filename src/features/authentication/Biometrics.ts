import { uas } from 'MyTontineConfig'
import {
  FaceScanBiometrics,
  type SDKConfig,
} from '../../../facetec-web-sdk/FaceBiometrics'
import { isDisabled } from '../DisabledLaunchFeatures'
import { FaceScanData } from './types/AuthMachineTypes.type'

const endpoints = {
  sessionToken: '/face_scan/session_token',
  faceAuth: '/face_scan/authenticate',
  faceEnroll: '/face_scan/enroll',
  idScan: '/v1/id_scan',
}

// Use only with the AuthMachine!

class FaceScanError extends Error {
  error: unknown
  errorData: string | undefined
  constructor({
    message,
    error,
    errorData,
  }: {
    message?: string
    error?: unknown
    errorData?: string
  }) {
    super(message)
    this.name = 'FaceScanError'
    this.error = error
    this.errorData = errorData
  }
}

/**
 * @private
 *
 * Starts a facescan with the passed in params and returns a response with
 * face scan data when scan has been completed successfully.
 *
 * A scan is considered successful only if the API responds with:
 * - Auth token
 * - User account info data
 * - Permissions
 *
 * Everything else is considered an error
 */
const _startFaceScan = async ({
  email,
  scanType,
  onEnrollmentOnlyDone,
  authToken,
}: {
  email: string
  scanType: 'AUTHENTICATION' | 'ENROLLMENT' | 'PHOTO_ID_SCAN'
  onEnrollmentOnlyDone?: (data: FaceScanData) => void
  authToken?: string
}): Promise<FaceScanData> => {
  // For enrollment and photo id scan we need an auth token

  if (isDisabled)
    return new Promise((resolve) => {
      resolve({
        authTokenInfo: {
          authToken: 'authToken',
          remainingTime: 0,
          refreshToken: '',
          permissions: 'read',
        },
        userAccountInfo: {
          closure_scheduled_time: '',
          email: '',
          email_verified: false,
          face_enrolled: false,
          id_rejection_reason: null,
          id_review_status: null,
          investment_account_status: 'rejected',
          is_admin: false,
          is_influencer: false,
          pin_set: false,
          referral_code_redeemed: null,
          referral_tier: 0,
          age: {
            age: 0,
            month: 0,
          },
          kyc_status: {
            L0: {
              passed_level: false,
              requirements: {
                email_verified: false,
              },
            },
            L1: {
              passed_level: false,
              requirements: {
                L0: false,
                face_scanned: false,
                id_verified: false,
                phone_verified: false,
              },
            },
            L2: {
              passed_level: false,
              requirements: {
                L1: false,
                address_verified: false,
              },
            },
          },
          ref_id: '',
          unverified_date_of_birth: '',
          unverified_age: {
            age: 0,
            month: 0,
          },
          unverified_date_of_birth_set_by_user: false,
          unverified_first_name: '',
          unverified_sex: 'Male',
          unverified_last_name: '',
          unverified_residency: '',
          unverified_phone_number: '',
        },
        enrollmentCompleted: true,
        idScanCompleted: true,
        error: undefined,
      })
    })

  const { baseUrl } = uas as unknown as {
    baseUrl: URL
  }

  return new Promise((resolve, reject) => {
    const handleCompletedScan = (data: FaceScanData) => {
      // Promise is only resolved if the scan has been completed successfully
      // and the API responds is the necessary data
      if (
        // We must check if there is an auth token, because the API can return a
        // 200 with data but not return an auth token, user_account_info and
        // permissions, because that scan is considered faulty and the API is
        // communicating with the web SDK
        data?.authTokenInfo.permissions &&
        data?.userAccountInfo &&
        // If there is no error, we resolve the promise
        !data.error
      ) {
        resolve(data)
      } else {
        reject(
          new FaceScanError({
            message: 'Missing data from FaceScanProcess callback',
            error: data?.error,
            errorData:
              'Error from FaceScanProcess callback, missing data from callback',
          })
        )
      }
    }

    FaceScanBiometrics.startScan({
      // Scan config
      baseUrl,
      endpoint: endpoints,
      // User passed in params
      email,
      authToken,
      scanType,
      onComplete: (data) =>
        handleCompletedScan(data as unknown as FaceScanData),
      onEnrollmentOnlyDone: (data) =>
        onEnrollmentOnlyDone?.(data as unknown as FaceScanData),
    })
  })
}

/**
 * @private
 *
 * Initializes the face scan SDK and persists the initialized status in `sessionStorage`.
 *
 * If the the SDK has already been initialized, it will return return the
 * `sessionStorage` entry, otherwise it will re-initialize the SDK
 */
const _initSDK = async (sdkConfig: SDKConfig) => {
  return new Promise((resolve, reject) => {
    const onSdkInitialized = (isInit: boolean) => {
      if (isInit) {
        resolve(isInit)
      } else {
        reject(
          new FaceScanError({
            error: `Failed to initialize SDK, might be server issue or browser issue preventing an init`,
          })
        )
      }
    }

    FaceScanBiometrics.initializeSDK(sdkConfig, onSdkInitialized)
  })
}

export { _initSDK, _startFaceScan }
