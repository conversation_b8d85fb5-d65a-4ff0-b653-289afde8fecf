import { useEffect, useState } from 'react'
import { useLocalization } from '../../../common/hooks/useLocalization'
import { generateRange } from '../../../common/utils/UtilFunctions.ts'
import { useAccountService } from '../../authentication/hooks/useAccountService'
import { calculateYearForRetirementAge } from '../../dashboard/utils/UtilFunctions'

/**
 * Takes in `user_details` and `ageThreshold` and calculates the retirement data
 * that is used by the UI inputs so the user can set their retirement month and
 * year
 */
export const useIncomeScheduler = (retirementData) => {
  const {
    context: { user_details },
  } = useAccountService()

  const { retirementYear, retirementMonth } = calculateYearForRetirementAge(
    retirementData?.contributionAge,
    retirementData?.retirementAge
  )

  // biome-ignore lint/style/noParameterAssign: <TODO: Solve>
  retirementData = {
    ...retirementData,
    year: retirementYear,
    month: retirementMonth,
    yearsOld: retirementData?.retirementAge?.age,
    monthsOld: retirementData?.retirementAge?.month,
  }

  const {
    detectedCountry: { tontinatorParams },
  } = useLocalization(user_details?.residency)

  //Income scheduling state
  const [scheduleRetirement, setScheduleRetirement] = useState(retirementData)
  //Generates slider steps, sliders steps are in state so when the user reaches
  //the range limit they can click the increment button so a new step is added
  //to the range. The range start if from age threshold, always. Age threshold
  //contains the minimum date that the user can set the slider to
  const [sliderSteps, setSliderSteps] = useState([])

  //Initializes the income scheduler with retirement data or if there is no
  //retirement data uses the age threshold
  useEffect(() => {
    if (tontinatorParams) {
      setScheduleRetirement(
        presetRetirementData(retirementData, tontinatorParams)
      )
      setSliderSteps(presetAgeSlider(tontinatorParams))
    }
  }, [
    user_details?.residency,
    ///////////////////
    user_details?.date_of_birth,
    tontinatorParams?.maxRetirementAge?.age,
    tontinatorParams?.maxRetirementAge?.month,
    tontinatorParams?.minRetirementAge?.age,
    tontinatorParams?.minRetirementAge?.month,
  ])

  return {
    sliderSteps,
    setSliderSteps,
    scheduleRetirement,
    setScheduleRetirement,
    isLoading: false,
    ageThreshold: tontinatorParams,
  }
}

//Presets the income scheduler data by checking if the user has already
//set their retirement month/year, if they have not then the age threshold
//data is used to preset the income scheduler
const presetRetirementData = (retirementData, ageThreshold) => {
  //Parses the age threshold values to number format
  const { year, month } = ageThreshold.minRetirementAgeYearMonth

  //Parses the age threshold values to number format
  const { age: yearsOld, month: monthsOld } = ageThreshold.minRetirementAge

  //Prioritize using the retirement data passed in from props, the reason being
  //because those values can be pension plan values
  //Retirement data MONTH and YEAR are in number format
  if (retirementData?.year > 0 && retirementData?.month >= 0) {
    return {
      ...retirementData,
      //If no values are provided then use age threshold values
      month: Math.max(retirementData?.month, month),
      year: Math.max(retirementData?.year, year),
      monthsOld: Math.max(retirementData?.monthsOld, monthsOld),
      yearsOld: Math.max(retirementData?.yearsOld, yearsOld),
    }
  }

  //Just returns the age thresholds
  return {
    ...retirementData,
    month,
    year,
    yearsOld,
    monthsOld,
  }
}

/**
 * Needs only the age threshold, because we want all MIN and MAX values, so the
 * user can stay within thresholds
 * Returns an array of numbers with all the possible ranges the slider can go up to.
 * @returns {Array<number>}
 */
const presetAgeSlider = (ageThreshold) => {
  //Sets age slider year to minimum from threshold age
  const { year: minStartYear } = ageThreshold.minRetirementAgeYearMonth

  //Sets age slider year to maximum
  const { year: maxStartYear } = ageThreshold.maxRetirementAgeYearMonth

  return generateRange(minStartYear, maxStartYear)
}
