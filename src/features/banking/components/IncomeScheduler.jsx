import PropTypes from 'prop-types'
import DateSlider from '../../../common/components/DateSlider'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { useAccountService } from '../../authentication/hooks/useAccountService'
import { useIncomeScheduler } from '../hooks/useIncomeScheduler'

/**
 * Scheduler used for picking user's retirement month/year. Only used in
 * authenticated UI because it needs `user_details` from the auth machine
 */
const IncomeScheduler = ({
  retirementData,
  setRetirementData,
  anonUserDetails,
}) => {
  const t = useTranslate()
  const {
    context: { user_details },
  } = useAccountService()

  const { sliderSteps, ageThreshold, scheduleRetirement } =
    // need the initial state just to init the hook because it uses legacy data
    // format
    useIncomeScheduler(retirementData)

  retirementData = {
    // only used for init and it is overridden by fresh retirement data from the
    // state
    ...scheduleRetirement,
    // only use fresh data from the state
    ...retirementData,
  }

  return (
    <DateSlider
      ageThresholds={ageThreshold}
      userDetails={
        anonUserDetails ??
        user_details ?? {
          // only used for blurred forecast
          date_of_birth: `${retirementData?.year}-${retirementData?.month}-01`,
        }
      }
      setSteps={undefined}
      label={t('INCOME_SLIDER_LABEL')}
      sliderSteps={sliderSteps}
      monthHeadLabel={t('MONTH_HEAD_LABEL')}
      yearHeadLabel={t('YEAR_HEAD_LABEL')}
      value={retirementData}
      yearsOldOnRetirementDateLabel={t('BUBBLE_YEAR_VALUE_LABEL')}
      yearsOldOnRetirementDate={retirementData?.yearsOld}
      monthsOldOnRetirementDateLabel={t('BUBBLE_MONTH_VALUE_LABEL')}
      monthsOldOnRetirementDate={retirementData?.monthsOld}
      caption={t('RETIRE_USER_INCOME_CAPTION')}
      setValue={setRetirementData}
    />
  )
}

IncomeScheduler.propTypes = {
  retirementData: PropTypes.object.isRequired,
  setRetirementData: PropTypes.func.isRequired,
  anonUserDetails: PropTypes.object,
}

export default IncomeScheduler
