import PropTypes from 'prop-types'
import Modal from '../../../common/components/Modal'
import { parseParamsForEmail } from '../../../common/utils/UtilFunctions'
import RegisterFormModal from '../../authentication/pages/RegisterFormModal'
import TontineDashboardLayout from '../components/TontineDashboardLayout'
import SandboxTontinatorPage from './SandboxTontinatorPage'

import style from '../../../common/style/BlurredForecastModal.module.scss'

/**
 * Renders a blurred Tontinator page
 */
const BlurredForecast = ({ incomeForecastParams, isAuthenticated }) => {
  return (
    <>
      {!isAuthenticated && (
        <Modal
          isOpen
          backdrop
          className="blurredForecastModal"
          customStyle={style}
        >
          <RegisterFormModal
            forecastPageRegisterModal
            forecastUserData={parseParamsForEmail(incomeForecastParams)}
          />
        </Modal>
      )}
      <div
        style={{
          // Blurs the forecast page for external users
          filter: 'blur(5px)',
        }}
      >
        <TontineDashboardLayout>
          <SandboxTontinatorPage />
        </TontineDashboardLayout>
      </div>
    </>
  )
}

BlurredForecast.propTypes = {
  incomeForecastParams: PropTypes.object,
  isAuthenticated: PropTypes.bool,
}

export default BlurredForecast
