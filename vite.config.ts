import { dirname, resolve } from 'path'
import { fileURLToPath } from 'url'
import { sentryVitePlugin } from '@sentry/vite-plugin'
import react from '@vitejs/plugin-react'
import { PluginOption, defineConfig } from 'vite'
import istanbul from 'vite-plugin-istanbul'
import { istanbulConfig } from './src/config/instrumenting'

// Needed for ES Modules to work with __dir
// Cypress wants it like that
const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

/**
 * ENV variables passed in from build scripts
 */
const envConfig = {
  port: process.env.PORT,
  environment: process.env.ENVIRONMENT ?? 'development',
  host: process.env.HOST,
  instrumentCode: process.env.INSTRUMENT_CODE
    ? JSON.parse(process.env.INSTRUMENT_CODE)
    : false,
  buildVariation: process.env.VITE_BUILD_VARIATION ?? 'full',
}

/**
 * Vite plugins, react must always be present!
 */
const plugins = [react()]

// Plugins added only in staging or prod env
if (envConfig.environment !== 'development') {
  plugins.push(
    sentryVitePlugin({
      authToken: process.env.SENTRY_AUTH_TOKEN,
      org: 'tontinetrust',
      project: 'my-tontine-web-application',
      debug: true,
      telemetry: false,
      sourcemaps: {
        // Deletes all sources maps
        filesToDeleteAfterUpload: ['**/*.js.map'],
      },
    }) as Array<PluginOption>
  )
}

// Plugins to be included only in dev env
if (envConfig.environment === 'development') {
  if (envConfig?.instrumentCode) {
    plugins.push(
      istanbul({
        ...istanbulConfig,
      }) as unknown as Array<PluginOption>
    )
  }
}

export default defineConfig(() => {
  return {
    //Shared general options with plugins array
    base: './',
    root: '.',
    jsx: 'react',
    // Plugins array is defined at the very top line 20
    // to be precise
    plugins,
    // Dev server runs with vite preview, give a preview of the app in
    // production
    server: {
      port: envConfig?.port as unknown as number,
      // Do not use ?? here
      host: envConfig?.host || '0.0.0.0',
    },

    // Preview server options, runs only when there is a bundle to serve
    preview: {
      port: envConfig?.port as unknown as number,
      // Do not use ?? here
      host: envConfig?.host || '0.0.0.0',
    },

    css: {
      modules: {
        generateScopedName:
          envConfig?.environment === 'development'
            ? `[local]`
            : // CSS File names are only hashed in production/staging
              `[hash:base64:5]`,
      },
    },

    resolve: {
      alias: {
        MyTontineConfig: resolve(
          __dirname,
          'src/config',
          `${envConfig?.environment}.js`
        ),
      },
    },

    // Build options when the app is built in production mode
    build: {
      // Source maps are included BUT deleted after uploaded to sentry!
      // Source maps must be always enabled for easier sentry debugging!
      sourcemap: true,
      rollupOptions: {
        output: {
          assetFileNames: '[hash:10].[ext]',
          chunkFileNames: '[hash:10].js',
          //Creates chunks from all the external dependencies the project uses
          //Dependencies are grouped by vendor, if a dependency is more than
          //100kB it is not bundled into a chunk and it is loaded as a
          //independent chunk, for example `ibantools` is loaded independently
          manualChunks: {
            'libphonenumber-js': ['libphonenumber-js'],
            'sentry-react': ['@sentry/react'],
            xstate: ['xstate', '@xstate/react'],
            d3: ['d3'],
            'react-dom': ['react-dom'],
            'react-router': ['react-router-dom'],
            toastify: ['react-toastify'],
            i18next: ['react-i18next', 'i18next-http-backend', 'i18next'],
            locize: ['locize-lastused', 'locize'],
            i18nUtils: [
              'i18next-localstorage-backend',
              'i18next-chained-backend',
              'i18next-browser-languagedetector',
            ],
            'lottie-web': ['lottie-web/build/player/lottie_light'],
            'react-utils': ['axios', 'dayjs', '@intercom/messenger-js-sdk'],
            'react-share': ['react-share'],
            'mixpanel-browser': ['mixpanel-browser'],
          },
        },
      },
    },
  }
})
