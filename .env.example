# Environment Variables Template
# Copy this file to .env.local for local development
# Set these variables in Netlify UI for production deployment

# Locize (Translation service)
VITE_LOCIZE_PROJECT_ID=your_locize_project_id
VITE_LOCIZE_API_KEY=your_locize_api_key

# Mixpanel (Analytics)
VITE_MIX_PANEL_API_KEY=your_mixpanel_api_key
VITE_MIX_PANEL_PROXY=https://mixpanel.mytontine.com

# Robo-actuary Backend
VITE_ROBO_BACKEND_URL=http://localhost:8082
VITE_ROBO_EMAIL_ENV=dev
VITE_ANALYTICS_TRACK=false

# App Configuration
VITE_ENVIRONMENT=dev
VITE_APP_ENV_COLOR=#f96767

# Sentry (Error tracking) - used by build plugin
SENTRY_ORG=your_sentry_org
SENTRY_PROJECT=your_sentry_project
SENTRY_AUTH_TOKEN=your_sentry_auth_token
